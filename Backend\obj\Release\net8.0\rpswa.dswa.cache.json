{"GlobalPropertiesHash": "ITtAFkNiSgdhUb2BwCQUf6iyFsK/CwOgeB9JuCIv+uU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=", "tbHjJNNBtg6umiynz2shqS662XjfezDb+CQ4P2UmKHM=", "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=", "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=", "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=", "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=", "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=", "zy6if5rum/KNJBMnHzYck6CVymjNyCx74ikTZBNKrFQ=", "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=", "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=", "eNbQtSumznkwG30VzfGYCUkn87Ysa16cJa0nZmCWvu0=", "5OS4nUzWLEo6ayBo7Bfipj4BmKEvgAOue2G35SWtC6E=", "CJdKsa+BGx3DjVsqqOXtBNiqk2DTfBKzC9UssZm6XCE=", "aeP7q8WGMklLxLcSIIcZsXBf9Klr0Np1Dlke+SAa3PY=", "515TIRz+IJhQtIXxlMac5pwwlivy5tCFqHrywQ+MReE=", "b0hC1zgkvwR45QY6snbPYRX8idjaM/bjPTUSwRGw2zA=", "WR9z+sCQnDlKVzMo2njfQb8H95HZmDBj9iJA1BUsABQ=", "jqR9y2J9M1mUQPWsZipuaep2ogczM8jKwK1IoUeSZMo=", "AZA934aG8YvL+enlVygn+lk9p3H4fai6s2hJuMYatBE=", "6pBbD5wY8mQcniCEOavkrwzkHVLxQxi5Zx0eKvyhyFA=", "rFdy83j40JS+eSEikH7fBNvN0+s4T8O/sPm/kyCDFos=", "JaW9p0ma/alwMdwvazrVtWi9i00b0wqOW16/S35RvNw=", "O4KoEdTQ7S+cOxtlmC0sGuj1tCCMpvIV3xuIEKnJer8=", "KG9CT9UGmpkMoEUAiimpq0hj8UgJdkD8pYkZ9awZoyU=", "VXtgL5yXxQ2SECENA9tYaQDWW93/pboQXYh8BNFE4DY=", "Fv8YebgEMtZ5nmPQkdoX20ZEiln4KztuzGUi1aJR2M8=", "co6eF5+rluklBduRRUjyuZpTdbxNDL6jQWQs5Fb0q9s=", "2UBguIeQHeVNM1kK3Ye+QMq84Ct7M5/gnmommfvMuOc=", "FLHVzZT9OTjbWdsNEbHGBPtp+zwWYDuxpMpCC/NFx9g=", "kD8hKAVhcAwYO0g2ypPB7gYrPzQBzf8vsO1cNs+3ydY=", "6WWcdJ5nnupXkqcuCC8iZsfoaFD/mU7XSMbUfr+9uNU=", "qFlo+EWtmLm+4U7Gxv51oXPqVQeMlk06V4S/Hpw/l8A=", "jXe8u9EcawqGf88DGXkdzKhAToMOHUqGeLa++Kjewro=", "bjhmkAOD5ycysjNspMn0Tf9MTC+FO4mNVQ4qSHIzHs8=", "/87ne7mi9k48sc3GDXkbuKQNiL7UhrkDXzCS3vk6gtA=", "okunsda/SZC2Hcg+iUBMQWWf0e3aHVSShYT5IOAx+3o=", "2zs207MjfPqE0Ayb1vqu1rdEMT8ExxjdHtdfWC0eXs8=", "DHnEM66g98FxbVd0ap+a93J3/rFpAsKAy1W6DOMZ/kk=", "UlnyY426PdkrxaYlto9F28BepMgz9//HXAxgE+bpfwU=", "gy5uJ0RwEHFaOCEDCcxPCHNv2RdOQjNlRYPlgoJruEE=", "DzzA+iKdHz9dPtja+xg8bSECPTZsT5JsICmrOzrFeZ0=", "R5OWUHt4NONn8d5T4AGUdtoQLQbc2ELtN7HnbkXd7Js=", "Bv/dhehd6UMGRaS62yXA9EuMLy/WQwer9Mnptkn5pPE=", "ft5D6n3cgfduNdv/NhJqglROo8Fb673I+BRyyKdAZG0=", "EHDXuUo7A5bfWtF06k9pu3jhPClBsDQ0g8hRn7hfhTs=", "O3V968n3tDk7wSoxPsQdmA5YTBij/QBbeVDZ3U3E4Wc=", "R5J6R8WLyuvSpgEbc+HV3B1M1c9bRXqyWTsvkP8ndtU=", "QLUvombc/kBW9R1v9WYIm7C+CVn9SmSCCVGjlVxCQeI=", "QQVyaVMM5jMq5pNuLpmFWmsM1CWxRRQJmZjz/Vm1cs8=", "NnNkKbR/wUf9//q4ATO0rcFkfSeO+z69Qso3nteLk8M=", "DNac8dBLnbnZ40mJdL31kDIzCERWgCZpthAC8mJSaKw=", "YWDpw39Zg5g7R32wfnbY1Z+w+yHd3xd2PK+LFklOlg0=", "ZpzbpJO4ytVMaxMdUCB0x36c3naGKrvX1fnTg9mPaDM=", "nZY3r+1iK1399iGOiQUaD2PbBiqHPYI4oG8mmPa5obk=", "H+2EPQGnFq8fEQUku1Wkzz0FPR1J39JkC5zbWgga0zo=", "3Se1H8MZq+15TLhAM9VcvT8Y1eWzWcWipY1VXX/rn/U=", "AXNjBtGJ55FLPeijtwpJK8i2vFJfLUKYAginv1a7Ufg=", "unVC5V/wv2o2n+1l5jvmoVjwwr3EPQZBPERL4xXJRQs=", "+pYTklN7fGxRhVoZGvMPNoXZKwd4eGfvl89lIa9yZOM=", "H09Tl1FXQoaRTW0dGH8tiHVbDwYikivaiOKUq9WitaE=", "lsTm1HBxim1HMZ3GvD+aSEJLhAn35JMPJV6czWfyC+8=", "DGvKmv09QMxcGjBXgya1cIOgkylGiJB9u57MLXQmbEc=", "yKoueoSq5oRSsQB7tdkxgKNjK/VtGpp/MkDDQ2m2sIU=", "m/8Z/5eZ7KYbSwl3TxkaDC7/nBeL2tdkg95aAEWKv/I=", "YIvgo4AeNU+s0B6grIJSCciNBSe4mWPj60aR/tkYrbI=", "xa6oQNDHdtLLbXm1j3iOaSSzEffnTT0s/xRM9oY7zxc=", "zJFnS9uu1YVBaUaSt28y9iB40ehqO+RJhM7GeWXhElE=", "r4a69znaVbjJGivk8yNYjpVErjEQEsptieviwbnhO68=", "bVU65fkRsUWkoCEHJbPR68gGL3hGTv1l9nBljK7RYQA=", "dvoLraiQukxHmzxS4MGtBXo57/5IEvILT3FLyn9a9fU=", "O6hprETRp6qx06sWVFXWN0vWOgAUhjhgHeqBJqcwVxs=", "ik9bgwvSBU3teylvUISd0gw8WzlXVbrxNNHLD/5cXI4=", "P7Bd0nE/Qftc4Q7GcL9NVTFsJ7GTWjhD1Q8DVNpPeNQ=", "HEmHvoZr1jMAvXMpEVBMp+k0noa8iJu2tQxLPq7F+Nw=", "GqZdWuw7fqQGkr9c+qEK9iTS/nqZ46Bpf+4uRlCyBOI=", "sdOW/R+O4Obf+esXvgPchevjm2gUmfPZFne9uCtyVlg=", "/ucrbrWRcuaUHxVQ+cFVM3oqDIFn14keOd44+7mpgfQ=", "hUT2PKlUbQvfwgc9Xox73ljQsZ5MEQSwj36MBZybWTo=", "4Vnl8RICajLiqvlBzcEB0JlnSEsQAAX9+8MZqqpPH2s=", "zaUO7uBf833+VMGeF2fchRBiqWpE47UHvlzz5NUmyvQ=", "JnXm7MgNcncF+z89HWDl9uXgybW5FUDv2f5giXJCldk=", "40n+EzCI9u12TyCs9SaDT4nn6IojvKtW7jap2ndw9ms=", "fjG1ZQnTUvg70Ew5rvNxKFEpRMC0qEnCvuwf8ZPSPdQ=", "2fToewxDxJ80EVKxbLDauWL9WkO097ACkgwOZGXzkaY=", "1w5SSwQlfkl6qO3qLdKz90p81hHohfvP5AuDnRcDZ+Q=", "p27zVPwbVZXLUi4BCgxBzdnYttDl3xKgCkulXc8I4u4=", "/Rn8aUoW9SBrT68AUl1j2p+Bimgr+skunnfvI/Z/WGs=", "L1qZua6CT/s0MMyZlIeL7V0qRv8xob4MJq+SMdNpBgs=", "ANegvSegk1bYBgryFUr1/XRdhgC5+AZVMrr3nc2wU9E=", "hwxkoBESpUG1VqnDaczLoud2v1ugwLwONSJHEn90Njo=", "avqE1PU91IsGmpsvEONm9TjSwm7dYNA/uKIf/PGskcA=", "rFuFXYNlP68d+gBGmf9zqZcSWU6uTUuBwXASxMJuQAs=", "/ai2jIWVPY+Vq/Wi0yk1dYf02os+rkPfpR4i1qqQ/Xc=", "jeqVUZTFCYEvZ0pA9BMuBZM1UtRGM6ADerklSHNzGZ4=", "9cFByTuan4+Gr1EgB6kpADl4TQsay7GoXsTFanN5X/A=", "ZReYdPAh2xCKnRO/8NOjCQTfWO4TmSe8/2prPiYQDOA=", "e7UMod7XGVRhrNP64mD14fWawcbqXEHHfa46/YfHAkk=", "/i5jW+Nx8hV1WlLOwjgEDPK6HfuTv3d0P+QUqrM8EcM=", "NMt7LhtMa5OV15AK2bWRMEP1SbYSd+9XiOKO2YQw3bw=", "JOyNszNmwlSOIGAeXaBrfbmHsPadvvpXReL6IspCCdQ=", "5VB5ebkKOtocQ1BUQ67S6Zp4vDackDVV/oMOi62zwH4=", "4lUvtyHJ5AcBPOBFW7lWgoqp0lvIryxAK7xFavkDego=", "PWUA30MX8K3xMKEPoUOF4ry1Kh45VxJ9HsQ/6GBPPQE=", "zDeV/GfZM0iurXD+D18tFduy6gEqALkEex9RzivAfyo=", "zBoB4dUVlL2Zg9Lk+Xfx4B5WUk++n4/w++PxHHKNwQs=", "pl/Gp3KMZmmm/VhEM8k6kORkYM3+WNKZ5o4XU5pK1Tc=", "DaM7XvL8oH8JUvfJ+yFJkzfMpJgjJ3KWMaRVynUTax8=", "HeyHG/uy2kvAJyzPg0arT2MTR5xNHM96ZRvu/N2Bbmw=", "muY/8qTarFN9qZSC1Ac4ohvYa8px4yMtI35CjnJ+5tA=", "cqBcdHTLAXT1YhzyGjZMW9W0Uo38ZbuyqetbY3iw6h8=", "x7/JWt5opn0C0D1uAQ1kH/1b2tIfJitdSjUM0nQBFN8=", "gVQTKSOqGM3KZcTlGgdmWVYc9V72eMGpj664+0c5OKY=", "UiA/ltVzRqONTeGT3UhbUZn0SY/2Md35gxYfwJvNkEc=", "IHbTHSiEb/AkTkwEsnow+rJMFSHXSwATg81xMgMPh7c=", "mdHcsro9Fy7jOrovYAIP8i/0vF60+N0wTcbD6GVx2jA=", "uAMpze3X9jn6uFUAcSEemWkQivqPLY0C3ah38mlhfgA=", "7Kw1qHUfZ18rPVNtMelrrEc+6GHUSDpRb+Mszs07M0A=", "gxbUvVG5i/BVP0wJmVhoMRKaVOYvIJWcwCbA7dT/+LE=", "fDWnD+4ivxZwPfL2jqY9mHtDe+pyA66F28cCAPq1hSs=", "ctL4G3VCkswrQjPZChaJwae1VXsCeA/HOC+eEM3I7cc=", "+3tM9Qp1sMHEFlQmfRAvwSiIsKgtk67cxyrj8AiFEbc=", "wjtx4c8HYnEo5Cy71E8kVXswOuEbhR7K0fHnq4kUbfs=", "N+i8I6VAaAQebnghLAxXTUj5HLWV+r5ILtMWISqFIBw=", "MVRXzobiQ0QbdMzErmaRcVwVyPqqXpd2K/MtPDNhIX0=", "NQ+R1d5XxeN4Tz17oUBM/KRLi4C5CiPxVLQdp0JY8xE=", "e1/PCUuWd41EBIaIRKwOnLNZ5XYvyYXogO/4FF5soao=", "BCU/w+AlJ62F/BrIKBcCI6uP+pbyCBWzxDoQJP8g1mI=", "IMz6u2Clj0e0TRVhqv3ZKgf8TZbuoG8twLHu7ruSLSI=", "ztQLnWSiASlZTZocmWuOt2beS25ADA9O/A6iBIir8K0=", "8NjeJlk5BRMZYY6rJ9J8wE0W3RTj3/fCHTO9jfrQJjw=", "NxI/cLhZPZBpWzbTVfXTVGtsEAl6kj/DvYfegRYvlHA=", "LD2fpSrwFfKsP0JrZLerqSLK6op+D15ZdwakSVDGsl4=", "+WjS1BPKk4oxX5Rd92v7usp0YF77pSSlI7JM3/lBo2k=", "Vt/hHySjv1z/KM4cqa0hk3h04fMj1FdLbzCqEPYY5gk=", "mWnUfzn67TwX35KEV8VAoZBYOV9Sc9w3ukaHIOo38b8=", "KNH7kVfIQdP8JUth8u/B4OPr5hw8eZBJijTgWnKRIqY=", "mbXu9AgsYtCtlTD8yPS3mkO3TyYcmjAmE0zAodwBf2I=", "dW9D5cwAgBajA8cdMoQJXUdAXTY4ssPmpm6lF5KW7TE=", "Xdthqo2aV+onJu9oSyFDYvVYceucdBcpIV95uDHVBwI=", "6pQDfVszT5mXqnRD+8EGHV+V/Ylt9VRv5Qf94FM46ZU=", "4COaGsccBMHmNF7wh5T3uxtG1/HJ2Udn7QtDCtqruv0=", "vZxBxeluPQBSpJeHaCWbK5lwkYxZC7tE/muWGKLsMNM=", "PqyPXHb4HsttwiRwwJgKW4p6Tfuo1agKYujQMk5vDWA=", "iOOcRjeAU9xr05qU5WwJ1Srct1snKFF6iBQcev2oHOU=", "JZyo/c+0va7O6Z1s+SK/q+104TCAg9CNci9Z36bruWc=", "lIdJRrohL6RbE2DFH9zM3z7UOI19aqf3rGnuxwGD75A=", "cRbn18LlHXtk7RkCoSBsByzBAW3DtXlIOJR/Q3XUGgQ=", "AhqikOv4467TXIua7OyxrgSaZQPuNUVqUB3p5u6m2VQ=", "gJPia7L0S1Q8tFE4FekbsYeyCPUSkfSpcnrjytH4D50=", "UiAkECuP6a5FRgzQqRuO/qwGvdxjZyBfCnE8jE+aKcQ=", "0YIvSG8GlBTt6DVNGbNu2KsrWob0lZTFjdrQeCiC4Mo=", "vR3u1E3JYSJVeBrkjsepEluMG20UaOcCljyzVxCKo/8=", "DrK/8+m3xUQo561TkNNpG+vlamFoY8nsjJxXOvtIOZ0=", "G0KAGbORc85tyLRrNICGlXjYnb1sXLoepa4tmgFcGeY=", "wob05Jt0vPYLj1YRgepcYXG09SREXAxaZGEbUGDRynM=", "sDqk6WsVXWFtkIxf1UAVP5cBMXYHzEPxVzY8u1hvgz4=", "sTxKEwO3xy9tZSttLlNIrUBavZdVrA/T5r4oHBsOMZY=", "TfNRtIKv3u7rj9wBjeKVerzcTIEWh+WQL2rw+nyCwM4=", "GwozmaXb2F/ZHQutXlzbKGTOj+YfjB3jZ8YskJERr5A=", "o8yfddgVdsjpuf1uhpjqDxApXitx0E/pex6zctd+AYA="], "CachedAssets": {"9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49.8475713+00:00"}, "zy6if5rum/KNJBMnHzYck6CVymjNyCx74ikTZBNKrFQ=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mte29tdte9", "Integrity": "dvu//CN7LKfuKxqfBNtqpDYRPpzXuTog66I/oarS69U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 51058, "LastWriteTime": "2025-08-08T20:14:05.4120435+00:00"}, "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h6nvgw2bxq", "Integrity": "GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6691, "LastWriteTime": "2025-08-08T20:14:05.6425962+00:00"}, "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39.2143566+00:00"}, "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26.6394667+00:00"}, "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34.6547862+00:00"}, "krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vwl5012ydz", "Integrity": "0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 11424, "LastWriteTime": "2025-08-08T19:01:32.3671149+00:00"}, "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16.6719653+00:00"}, "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uorbu8vuak", "Integrity": "7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 5567, "LastWriteTime": "2025-08-08T20:07:03.6376846+00:00"}, "tbHjJNNBtg6umiynz2shqS662XjfezDb+CQ4P2UmKHM=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "drq4formlr", "Integrity": "h6D7MxseOwe6r+AISkd/NlRrL7LCW8lqkHoN3HOBKio=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 17666, "LastWriteTime": "2025-08-08T20:14:05.6106063+00:00"}}, "CachedCopyCandidates": {}}